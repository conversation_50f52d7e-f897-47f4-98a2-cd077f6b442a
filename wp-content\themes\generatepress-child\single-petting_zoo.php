<?php
/**
 * Single Petting Zoo Template - Redesigned Layout
 */

get_header();

// Get all the data we'll need
$post_id = get_the_ID();
$address = get_post_meta($post_id, '_petting_zoo_address', true);
$phone = get_post_meta($post_id, '_petting_zoo_phone', true);
$website = get_post_meta($post_id, '_petting_zoo_website', true);
$hours = get_post_meta($post_id, '_petting_zoo_hours', true);
$latitude = get_post_meta($post_id, '_petting_zoo_latitude', true);
$longitude = get_post_meta($post_id, '_petting_zoo_longitude', true);
$google_maps_url = get_post_meta($post_id, '_petting_zoo_google_maps_url', true);
$youtube_url = get_post_meta($post_id, '_petting_zoo_youtube', true);
$pics_data = get_post_meta($post_id, '_petting_zoo_pics', true);
$pics = $pics_data ? json_decode($pics_data, true) : array();

// Get first picture for hero background
$hero_bg_image = '';
if (!empty($pics) && isset($pics[0]['url'])) {
    $hero_bg_image = $pics[0]['url'];
} else {
    // Fallback to placeholder image
    $hero_bg_image = '/wp-content/uploads/2025/06/pettingzoophoto (1).webp';
}
?>

<div id="primary" class="content-area full-width-content">
    <main id="main" class="site-main">

        <?php while (have_posts()) : the_post(); ?>

            <article id="post-<?php the_ID(); ?>" <?php post_class('zoo-single-page'); ?>>

                <!-- Full-Width Hero Section with Background Image -->
                <div class="zoo-hero-section" <?php if ($hero_bg_image) : ?>style="background-image: url('<?php echo esc_url($hero_bg_image); ?>');"<?php endif; ?>>
                    <div class="hero-overlay"></div>
                    <div class="hero-content-wrapper">
                        <div class="hero-content">
                            <h1 class="zoo-title"><?php the_title(); ?></h1>

                            <?php if ($address) : ?>
                                <div class="zoo-address">
                                    <span class="address-icon">📍</span>
                                    <?php echo esc_html($address); ?>
                                </div>
                            <?php endif; ?>

                            <!-- Quick Action Buttons -->
                            <div class="hero-actions">
                                <?php if ($phone) : ?>
                                    <a href="tel:<?php echo esc_attr($phone); ?>" class="hero-btn">
                                        <span class="icon">📞</span>
                                        Call Now
                                    </a>
                                <?php endif; ?>

                                <?php if ($website) : ?>
                                    <a href="<?php echo esc_url($website); ?>" target="_blank" class="hero-btn">
                                        <span class="icon">🌐</span>
                                        Visit Website
                                    </a>
                                <?php endif; ?>

                                <button class="hero-btn directions-btn" onclick="getDirections()">
                                    <span class="icon">🗺️</span>
                                    Get Directions
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Centered Container Layout -->
                <div class="container">

                    <!-- Basic Info + Map Section -->
                    <section class="zoo-info-map-section">
                        <div class="info-map-grid">

                            <!-- Left Side: Basic Info & Hours -->
                            <div class="basic-info-panel">
                                <h2>Visit Information</h2>

                                <!-- Description -->
                                <div class="zoo-description">
                                    <h3>About <?php the_title(); ?></h3>
                                    <div class="content">
                                        <?php the_content(); ?>
                                    </div>
                                </div>

                                <!-- Google Rating -->
                                <?php
                                $rating = get_post_meta($post_id, '_petting_zoo_rating', true);
                                $rating_count = get_post_meta($post_id, '_petting_zoo_rating_count', true);

                                if ($rating && $rating_count) : ?>
                                    <div class="zoo-rating">
                                        <h4>Google Rating</h4>
                                        <div class="rating-display">
                                            <span class="rating-stars">
                                                <?php
                                                $full_stars = floor($rating);
                                                $half_star = ($rating - $full_stars) >= 0.5;

                                                for ($i = 1; $i <= 5; $i++) {
                                                    if ($i <= $full_stars) {
                                                        echo '⭐';
                                                    } elseif ($i == $full_stars + 1 && $half_star) {
                                                        echo '⭐';
                                                    } else {
                                                        echo '☆';
                                                    }
                                                }
                                                ?>
                                            </span>
                                            <span class="rating-text"><?php echo esc_html($rating); ?>/5 (<?php echo number_format($rating_count); ?> reviews)</span>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- Hours -->
                                <?php if ($hours) : ?>
                                    <div class="opening-hours">
                                        <h4>Opening Hours</h4>
                                        <div class="hours-list">
                                            <?php
                                            // Parse hours and format them nicely
                                            $hours_lines = explode("\n", $hours);
                                            foreach ($hours_lines as $hour_line) {
                                                $hour_line = trim($hour_line);
                                                if (!empty($hour_line)) {
                                                    // Try to split day and time
                                                    if (preg_match('/^([^:]+):\s*(.+)$/', $hour_line, $matches)) {
                                                        echo '<div class="hours-row">';
                                                        echo '<span class="day">' . esc_html(trim($matches[1])) . '</span>';
                                                        echo '<span class="time">' . esc_html(trim($matches[2])) . '</span>';
                                                        echo '</div>';
                                                    } else {
                                                        echo '<div class="hours-row">';
                                                        echo '<span class="full-line">' . esc_html($hour_line) . '</span>';
                                                        echo '</div>';
                                                    }
                                                }
                                            }
                                            ?>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- Contact Info -->
                                <div class="contact-info">
                                    <h4>Contact Information</h4>
                                    <?php if ($phone) : ?>
                                        <div class="contact-item">
                                            <span class="icon">📞</span>
                                            <a href="tel:<?php echo esc_attr($phone); ?>"><?php echo esc_html($phone); ?></a>
                                        </div>
                                    <?php endif; ?>

                                    <?php if ($website) : ?>
                                        <div class="contact-item">
                                            <span class="icon">🌐</span>
                                            <a href="<?php echo esc_url($website); ?>" target="_blank">Visit Website</a>
                                        </div>
                                    <?php endif; ?>

                                    <?php if ($address) : ?>
                                        <div class="contact-item">
                                            <span class="icon">📍</span>
                                            <span><?php echo esc_html($address); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Right Side: Google Maps -->
                            <div class="map-panel">
                                <h3>Location</h3>
                                <?php if ($latitude && $longitude) : ?>
                                    <div class="zoo-map-container">
                                        <div id="zoo-google-map"
                                             data-lat="<?php echo esc_attr($latitude); ?>"
                                             data-lng="<?php echo esc_attr($longitude); ?>"
                                             data-title="<?php echo esc_attr(get_the_title()); ?>"
                                             data-address="<?php echo esc_attr($address); ?>">
                                        </div>
                                        <div class="map-actions">
                                            <a href="https://maps.google.com/?q=<?php echo esc_attr($latitude); ?>,<?php echo esc_attr($longitude); ?>"
                                               target="_blank" class="btn btn-primary">
                                                View on Google Maps
                                            </a>
                                            <button class="btn btn-secondary" onclick="getDirections()">
                                                Get Directions
                                            </button>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>

                        </div>
                    </section>

                    <!-- YouTube Video Section -->
                    <?php if ($youtube_url) : ?>
                        <section class="zoo-video-section">
                            <div class="section-header">
                                <h2>🎥 Take a Virtual Tour</h2>
                                <p>Get a preview of what awaits you at <?php the_title(); ?> with this video tour. See the animals, facilities, and experiences that make this petting zoo special.</p>
                            </div>
                            <div class="video-container">
                                <?php
                                // Convert YouTube URL to embed format
                                $embed_url = '';
                                if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/', $youtube_url, $matches)) {
                                    $video_id = $matches[1];
                                    $embed_url = "https://www.youtube.com/embed/{$video_id}?rel=0&showinfo=0";
                                }
                                ?>
                                <?php if ($embed_url) : ?>
                                    <div class="responsive-video">
                                        <iframe src="<?php echo esc_url($embed_url); ?>"
                                                frameborder="0"
                                                allowfullscreen
                                                title="<?php echo esc_attr(get_the_title()); ?> Video Tour">
                                        </iframe>
                                    </div>
                                <?php else : ?>
                                    <div class="video-link">
                                        <a href="<?php echo esc_url($youtube_url); ?>" target="_blank" class="btn btn-primary">
                                            <span class="icon">🎥</span>
                                            Watch Video Tour
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </section>
                    <?php endif; ?>



                    <!-- Animals Section -->
                    <?php
                    $animals = get_the_terms($post_id, 'animal_type');
                    $animals_data = get_post_meta($post_id, '_petting_zoo_animals_data', true);

                    if ($animals && !is_wp_error($animals)) : ?>
                        <section class="zoo-animals-section">
                            <h2>🐾 Animals You Can Meet</h2>

                            <?php if ($animals_data) :
                                $animals_info = json_decode($animals_data, true);
                                if (isset($animals_info['description'])) : ?>
                                    <p class="section-description"><?php echo esc_html($animals_info['description']); ?></p>
                                <?php endif;

                                if (isset($animals_info['exoticAnimals']) && $animals_info['exoticAnimals'] === 'Yes') : ?>
                                    <div class="exotic-animals-notice">
                                        <span class="icon">🦋</span>
                                        <strong>Exotic Animals Available!</strong> This location features unique and exotic animal encounters.
                                    </div>
                                <?php endif;
                            endif; ?>

                            <div class="animal-tags-grid">
                                <?php
                                // Animal icons mapping
                                $animal_icons = array(
                                    'goats' => '🐐', 'sheep' => '🐑', 'pigs' => '🐷', 'cows' => '🐄', 'horses' => '🐴',
                                    'chickens' => '🐔', 'ducks' => '🦆', 'rabbits' => '🐰', 'llamas' => '🦙', 'alpacas' => '🦙',
                                    'donkeys' => '🫏', 'ponies' => '🐴', 'turkeys' => '🦃', 'geese' => '🪿', 'guinea pigs' => '🐹',
                                    'pandas' => '🐼', 'elephants' => '🐘', 'giraffes' => '🦒', 'lions' => '🦁', 'tigers' => '🐅',
                                    'bears' => '🐻', 'monkeys' => '🐒', 'zebras' => '🦓', 'rhinos' => '🦏', 'hippos' => '🦛'
                                );

                                foreach ($animals as $animal) :
                                    $animal_name = strtolower($animal->name);
                                    $icon = '🐾'; // Default icon
                                    foreach ($animal_icons as $key => $emoji) {
                                        if (strpos($animal_name, $key) !== false) {
                                            $icon = $emoji;
                                            break;
                                        }
                                    }
                                ?>
                                    <a href="<?php echo get_term_link($animal); ?>" class="animal-tag">
                                        <div class="animal-icon-large"><?php echo $icon; ?></div>
                                        <div class="animal-name"><?php echo esc_html($animal->name); ?></div>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        </section>
                    <?php endif; ?>

                    <!-- Additional Information Section -->
                    <?php
                    // Get additional data
                    $good_for_children = get_post_meta($post_id, '_petting_zoo_good_for_children', true);
                    $payment_options_data = get_post_meta($post_id, '_petting_zoo_payment_options', true);
                    $parking_options_data = get_post_meta($post_id, '_petting_zoo_parking_options', true);
                    $accessibility_options_data = get_post_meta($post_id, '_petting_zoo_accessibility_options', true);
                    $activities_data = get_post_meta($post_id, '_petting_zoo_activities_data', true);

                    $payment_options = $payment_options_data ? json_decode($payment_options_data, true) : array();
                    $parking_options = $parking_options_data ? json_decode($parking_options_data, true) : array();
                    $accessibility_options = $accessibility_options_data ? json_decode($accessibility_options_data, true) : array();
                    $activities = $activities_data ? json_decode($activities_data, true) : array();
                    ?>

                    <section class="zoo-detailed-info-section">
                        <h2>📋 Detailed Information</h2>

                        <div class="info-categories-grid">

                            <!-- Family & Children -->
                            <div class="info-category">
                                <h3>👨‍👩‍👧‍👦 Family & Children</h3>
                                <div class="info-items">
                                    <div class="info-item">
                                        <span class="info-icon"><?php echo ($good_for_children === 'yes') ? '✅' : '❌'; ?></span>
                                        <span class="info-label">Good for Children</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Options -->
                            <?php if (!empty($payment_options)) : ?>
                                <div class="info-category">
                                    <h3>💳 Payment Options</h3>
                                    <div class="info-items">
                                        <?php if (isset($payment_options['acceptsDebitCards'])) : ?>
                                            <div class="info-item">
                                                <span class="info-icon"><?php echo $payment_options['acceptsDebitCards'] ? '✅' : '❌'; ?></span>
                                                <span class="info-label">Debit Cards</span>
                                            </div>
                                        <?php endif; ?>

                                        <?php if (isset($payment_options['acceptsCashOnly'])) : ?>
                                            <div class="info-item">
                                                <span class="info-icon"><?php echo $payment_options['acceptsCashOnly'] ? '✅' : '❌'; ?></span>
                                                <span class="info-label">Cash Only</span>
                                            </div>
                                        <?php endif; ?>

                                        <?php if (isset($payment_options['acceptsNfc'])) : ?>
                                            <div class="info-item">
                                                <span class="info-icon"><?php echo $payment_options['acceptsNfc'] ? '✅' : '❌'; ?></span>
                                                <span class="info-label">Contactless Payment</span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Parking Options -->
                            <?php if (!empty($parking_options)) : ?>
                                <div class="info-category">
                                    <h3>🚗 Parking</h3>
                                    <div class="info-items">
                                        <?php if (isset($parking_options['paidParkingLot'])) : ?>
                                            <div class="info-item">
                                                <span class="info-icon"><?php echo $parking_options['paidParkingLot'] ? '✅' : '❌'; ?></span>
                                                <span class="info-label">Paid Parking Lot</span>
                                            </div>
                                        <?php endif; ?>

                                        <?php if (isset($parking_options['paidGarageParking'])) : ?>
                                            <div class="info-item">
                                                <span class="info-icon"><?php echo $parking_options['paidGarageParking'] ? '✅' : '❌'; ?></span>
                                                <span class="info-label">Paid Garage Parking</span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Accessibility -->
                            <?php if (!empty($accessibility_options)) : ?>
                                <div class="info-category">
                                    <h3>♿ Accessibility</h3>
                                    <div class="info-items">
                                        <?php if (isset($accessibility_options['wheelchairAccessibleParking'])) : ?>
                                            <div class="info-item">
                                                <span class="info-icon"><?php echo $accessibility_options['wheelchairAccessibleParking'] ? '✅' : '❌'; ?></span>
                                                <span class="info-label">Wheelchair Accessible Parking</span>
                                            </div>
                                        <?php endif; ?>

                                        <?php if (isset($accessibility_options['wheelchairAccessibleEntrance'])) : ?>
                                            <div class="info-item">
                                                <span class="info-icon"><?php echo $accessibility_options['wheelchairAccessibleEntrance'] ? '✅' : '❌'; ?></span>
                                                <span class="info-label">Wheelchair Accessible Entrance</span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Activities -->
                            <?php if (!empty($activities)) : ?>
                                <div class="info-category">
                                    <h3>🎯 Activities & Attractions</h3>
                                    <div class="info-items">
                                        <?php
                                        $activity_items = array(
                                            'playgroundAvailable' => 'Playground',
                                            'mazeAvailable' => 'Maze',
                                            'rideAvailable' => 'Rides',
                                            'drivethroughAvailable' => 'Drive-Through',
                                            'aquariumAvailable' => 'Aquarium',
                                            'miningAvailable' => 'Mining Activity'
                                        );

                                        foreach ($activity_items as $key => $label) :
                                            if (isset($activities[$key])) : ?>
                                                <div class="info-item">
                                                    <span class="info-icon"><?php echo ($activities[$key] === 'Yes') ? '✅' : '❌'; ?></span>
                                                    <span class="info-label"><?php echo $label; ?></span>
                                                </div>
                                            <?php endif;
                                        endforeach; ?>
                                    </div>
                                </div>
                            <?php endif; ?>

                        </div>
                    </section>

                    <!-- Enhanced Features & Amenities & Food Section -->
                    <?php
                    $features = get_the_terms($post_id, 'features');
                    $food_amenities = get_post_meta($post_id, '_petting_zoo_food_amenities', true);

                    if (($features && !is_wp_error($features)) || $food_amenities) : ?>
                        <section class="zoo-features-amenities-section">
                            <h2>🏛️ Features & Amenities & Food</h2>

                            <?php if ($food_amenities) : ?>
                                <p class="section-description"><?php echo esc_html($food_amenities); ?></p>
                            <?php endif; ?>

                            <?php if ($features && !is_wp_error($features)) : ?>
                                <div class="features-grid">
                                    <?php foreach ($features as $feature) : ?>
                                        <div class="feature-item">
                                            <span class="feature-icon">✨</span>
                                            <span class="feature-name"><?php echo esc_html($feature->name); ?></span>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </section>
                    <?php endif; ?>

                    <!-- Enhanced Special Events & Services Section -->
                    <?php
                    $events = get_the_terms($post_id, 'event_type');
                    if ($events && !is_wp_error($events)) : ?>
                        <section class="zoo-events-section">
                            <h2>🎉 Special Events & Services</h2>
                            <p class="section-description">Discover the special events and services available at <?php the_title(); ?>. Perfect for celebrations, educational experiences, and memorable occasions.</p>

                            <div class="events-grid">
                                <?php foreach ($events as $event) : ?>
                                    <div class="event-card">
                                        <div class="event-icon">🎊</div>
                                        <div class="event-content">
                                            <h4><?php echo esc_html($event->name); ?></h4>
                                            <?php if ($event->description) : ?>
                                                <p><?php echo esc_html($event->description); ?></p>
                                            <?php endif; ?>
                                        </div>
                                        <div class="event-action">
                                            <span class="availability-badge">Available</span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </section>
                    <?php endif; ?>

                    <!-- Testimonials & Reviews Section -->
                    <?php
                    $reviews_description = get_post_meta($post_id, '_petting_zoo_reviews_description', true);
                    $reviews_data = get_post_meta($post_id, '_petting_zoo_reviews', true);
                    $reviews = $reviews_data ? json_decode($reviews_data, true) : array();
                    $rating = get_post_meta($post_id, '_petting_zoo_rating', true);
                    $rating_count = get_post_meta($post_id, '_petting_zoo_rating_count', true);

                    if ($reviews_description || !empty($reviews) || ($rating && $rating_count)) : ?>
                        <section class="zoo-testimonials-section">
                            <h2>⭐ What Families Are Saying</h2>

                            <!-- Google Reviews Rating Prominently Displayed -->
                            <?php if ($rating && $rating_count) : ?>
                                <div class="google-reviews-highlight">
                                    <div class="google-reviews-header">
                                        <h3>Google Reviews</h3>
                                    </div>
                                    <div class="rating-showcase">
                                        <div class="rating-number"><?php echo esc_html($rating); ?></div>
                                        <div class="rating-stars-large">
                                            <?php
                                            $full_stars = floor($rating);
                                            $half_star = ($rating - $full_stars) >= 0.5;

                                            for ($i = 1; $i <= 5; $i++) {
                                                if ($i <= $full_stars) {
                                                    echo '<span class="star filled">⭐</span>';
                                                } elseif ($i == $full_stars + 1 && $half_star) {
                                                    echo '<span class="star filled">⭐</span>';
                                                } else {
                                                    echo '<span class="star empty">☆</span>';
                                                }
                                            }
                                            ?>
                                        </div>
                                        <div class="rating-count"><?php echo number_format($rating_count); ?> reviews</div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if ($reviews_description) : ?>
                                <div class="reviews-summary">
                                    <p class="summary-text"><?php echo esc_html($reviews_description); ?></p>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($reviews)) : ?>
                                <div class="testimonials-grid">
                                    <?php
                                    $review_count = 0;
                                    foreach ($reviews as $review) :
                                        if ($review_count >= 5) break; // Limit to 5 reviews
                                        $review_count++;

                                        $rating = isset($review['rating']) ? intval($review['rating']) : 5;
                                        $review_text = isset($review['text']['text']) ? $review['text']['text'] : '';
                                        $author_name = isset($review['authorAttribution']['displayName']) ? $review['authorAttribution']['displayName'] : 'Anonymous';
                                        $publish_date = isset($review['publishTime']) ? $review['publishTime'] : '';

                                        // Format date
                                        $formatted_date = '';
                                        if ($publish_date) {
                                            $date = DateTime::createFromFormat('Y-m-d\TH:i:s.u\Z', $publish_date);
                                            if ($date) {
                                                $formatted_date = $date->format('F Y');
                                            }
                                        }
                                    ?>
                                        <div class="testimonial-card">
                                            <div class="testimonial-header">
                                                <div class="reviewer-info">
                                                    <h4><?php echo esc_html($author_name); ?></h4>
                                                    <?php if ($formatted_date) : ?>
                                                        <span class="review-date"><?php echo esc_html($formatted_date); ?></span>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="review-rating">
                                                    <?php for ($i = 1; $i <= 5; $i++) : ?>
                                                        <span class="star <?php echo ($i <= $rating) ? 'filled' : 'empty'; ?>">⭐</span>
                                                    <?php endfor; ?>
                                                </div>
                                            </div>
                                            <div class="testimonial-content">
                                                <p>"<?php echo esc_html(wp_trim_words($review_text, 30)); ?>"</p>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </section>
                    <?php endif; ?>



                    <!-- Enhanced What to Expect Section -->
                    <?php
                    $pros_cons_data = get_post_meta($post_id, '_petting_zoo_pros_cons', true);
                    if ($pros_cons_data) :
                        $pros_cons = json_decode($pros_cons_data, true); ?>
                        <section class="zoo-expectations-section">
                            <h2>📊 What to Expect at <?php the_title(); ?></h2>
                            <p class="section-description">Get a realistic overview of your visit with insights from other families who have experienced this petting zoo.</p>

                            <div class="expectations-grid">
                                <?php if (isset($pros_cons['pros']) && is_array($pros_cons['pros'])) : ?>
                                    <div class="pros-panel">
                                        <div class="panel-header">
                                            <h3><span class="icon">✅</span> What Families Love</h3>
                                        </div>
                                        <div class="panel-content">
                                            <?php foreach ($pros_cons['pros'] as $pro) : ?>
                                                <div class="expectation-item positive">
                                                    <span class="item-icon">👍</span>
                                                    <span class="item-text"><?php echo esc_html($pro); ?></span>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <?php if (isset($pros_cons['cons']) && is_array($pros_cons['cons'])) : ?>
                                    <div class="cons-panel">
                                        <div class="panel-header">
                                            <h3><span class="icon">⚠️</span> Things to Keep in Mind</h3>
                                        </div>
                                        <div class="panel-content">
                                            <?php foreach ($pros_cons['cons'] as $con) : ?>
                                                <div class="expectation-item consideration">
                                                    <span class="item-icon">💭</span>
                                                    <span class="item-text"><?php echo esc_html($con); ?></span>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </section>
                    <?php endif; ?>

                    <!-- Nearby Petting Zoos Section with Split Layout -->
                    <section class="nearby-zoos-section-split">
                        <div class="nearby-zoos-container">
                            <!-- Left Panel: Current Zoo Info Summary -->
                            <div class="current-zoo-panel">
                                <h2><?php the_title(); ?> Info:</h2>

                                <!-- Address -->
                                <?php if ($address) : ?>
                                    <div class="info-item">
                                        <span class="info-icon">📍</span>
                                        <span class="info-text"><?php echo esc_html($address); ?></span>
                                    </div>
                                <?php endif; ?>

                                <!-- Phone -->
                                <?php if ($phone) : ?>
                                    <div class="info-item">
                                        <span class="info-icon">📞</span>
                                        <a href="tel:<?php echo esc_attr($phone); ?>" class="info-link"><?php echo esc_html($phone); ?></a>
                                    </div>
                                <?php endif; ?>

                                <!-- Website -->
                                <?php if ($website) : ?>
                                    <div class="info-item">
                                        <span class="info-icon">🌐</span>
                                        <a href="<?php echo esc_url($website); ?>" target="_blank" class="info-link">Visit Website</a>
                                    </div>
                                <?php endif; ?>

                                <!-- Current Status -->
                                <div class="info-item status-item">
                                    <span class="info-icon status-open">🟢</span>
                                    <span class="info-text">Open now:
                                        <?php
                                        if ($hours) {
                                            $hours_lines = explode("\n", $hours);
                                            $today = date('l'); // Get current day name
                                            foreach ($hours_lines as $hour_line) {
                                                if (stripos($hour_line, $today) !== false) {
                                                    $parts = explode(':', $hour_line, 2);
                                                    if (isset($parts[1])) {
                                                        echo esc_html(trim($parts[1]));
                                                        break;
                                                    }
                                                }
                                            }
                                        } else {
                                            echo "Check website for hours";
                                        }
                                        ?>
                                    </span>
                                </div>

                                <!-- Days of Week -->
                                <?php if ($hours) : ?>
                                    <div class="hours-summary">
                                        <?php
                                        $hours_lines = explode("\n", $hours);
                                        $days_shown = 0;
                                        foreach ($hours_lines as $hour_line) {
                                            if ($days_shown >= 7) break;
                                            $hour_line = trim($hour_line);
                                            if (!empty($hour_line) && preg_match('/^([^:]+):\s*(.+)$/', $hour_line, $matches)) {
                                                $day = trim($matches[1]);
                                                $time = trim($matches[2]);
                                                $is_today = (date('l') === $day);
                                                echo '<div class="day-hours' . ($is_today ? ' today' : '') . '">';
                                                echo '<span class="day">' . esc_html($day) . '</span>';
                                                echo '<span class="time">' . esc_html($time) . '</span>';
                                                echo '</div>';
                                                $days_shown++;
                                            }
                                        }
                                        ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Right Panel: Map and Nearby Zoos -->
                            <div class="map-and-nearby-panel">
                                <!-- Map Section -->
                                <?php if ($latitude && $longitude) : ?>
                                    <div class="nearby-map-container">
                                        <div id="nearby-zoos-map"
                                             data-lat="<?php echo esc_attr($latitude); ?>"
                                             data-lng="<?php echo esc_attr($longitude); ?>"
                                             data-title="<?php echo esc_attr(get_the_title()); ?>">
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- Nearby Zoos List -->
                                <div class="nearby-zoos-list">
                                    <h3>More Petting Zoos Near You</h3>

                                    <?php
                                    // Get current zoo's location terms
                                    $locations = get_the_terms($post_id, 'location');
                                    $related_args = array(
                                        'post_type' => 'petting_zoo',
                                        'posts_per_page' => 3,
                                        'post__not_in' => array($post_id),
                                        'orderby' => 'rand'
                                    );

                                    // Add location-based relation if available
                                    if ($locations && !is_wp_error($locations)) {
                                        $location_ids = wp_list_pluck($locations, 'term_id');
                                        $related_args['tax_query'] = array(
                                            array(
                                                'taxonomy' => 'location',
                                                'field' => 'term_id',
                                                'terms' => $location_ids,
                                            )
                                        );
                                    }

                                    $nearby_query = new WP_Query($related_args);

                                    if ($nearby_query->have_posts()) : ?>
                                        <?php while ($nearby_query->have_posts()) : $nearby_query->the_post(); ?>
                                            <div class="nearby-zoo-item">
                                                <?php if (has_post_thumbnail()) : ?>
                                                    <div class="zoo-thumbnail">
                                                        <a href="<?php the_permalink(); ?>">
                                                            <?php the_post_thumbnail('thumbnail'); ?>
                                                        </a>
                                                    </div>
                                                <?php endif; ?>

                                                <div class="zoo-details">
                                                    <h4><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h4>

                                                    <?php
                                                    $nearby_address = get_post_meta(get_the_ID(), '_petting_zoo_address', true);
                                                    if ($nearby_address) : ?>
                                                        <div class="zoo-address">
                                                            <span class="icon">📍</span>
                                                            <?php echo esc_html(wp_trim_words($nearby_address, 8)); ?>
                                                        </div>
                                                    <?php endif; ?>

                                                    <div class="zoo-distance">
                                                        <span class="distance-badge">🔗 1.03 miles</span>
                                                    </div>

                                                    <a href="<?php the_permalink(); ?>" class="view-details-btn">View Center Details</a>
                                                </div>
                                            </div>
                                        <?php endwhile; ?>
                                    <?php else : ?>
                                        <p class="no-nearby-zoos">No nearby petting zoos found in this area.</p>
                                    <?php endif;
                                    wp_reset_postdata(); ?>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- State Cities Section -->
                    <?php
                    // Get the state from location taxonomy
                    $state_name = '';
                    if ($locations && !is_wp_error($locations)) {
                        foreach ($locations as $location) {
                            if ($location->parent == 0) { // Parent terms are states
                                $state_name = $location->name;
                                break;
                            }
                        }
                    }

                    if ($state_name) :
                        // Read cities from CSV file
                        $csv_file = get_template_directory() . '/../../../uploads/2025/06/city-state.csv';
                        $cities_in_state = array();

                        if (file_exists($csv_file)) {
                            $handle = fopen($csv_file, 'r');
                            $header = fgetcsv($handle); // Skip header row

                            while (($data = fgetcsv($handle)) !== FALSE) {
                                if (isset($data[2]) && $data[2] === $state_name) {
                                    $cities_in_state[] = $data[0]; // city_ascii
                                }
                            }
                            fclose($handle);
                        }

                        if (!empty($cities_in_state)) : ?>
                            <section class="state-cities-section">
                                <h2>🏙️ Other Cities in <?php echo esc_html($state_name); ?></h2>
                                <p class="section-description">Explore petting zoos in other cities throughout <?php echo esc_html($state_name); ?>. Find the perfect family destination near you!</p>

                                <div class="cities-badges">
                                    <?php
                                    // Limit to 20 cities and randomize
                                    shuffle($cities_in_state);
                                    $cities_to_show = array_slice($cities_in_state, 0, 20);

                                    foreach ($cities_to_show as $city) : ?>
                                        <a href="<?php echo home_url('/city/' . sanitize_title($city) . '-' . sanitize_title($state_name)); ?>" class="city-badge">
                                            <?php echo esc_html($city); ?>
                                        </a>
                                    <?php endforeach; ?>
                                </div>
                            </section>
                        <?php endif; ?>
                    <?php endif; ?>

                    <!-- Planning Your Visit Section -->
                    <?php
                    $visitor_tips = get_post_meta($post_id, '_petting_zoo_visitor_tips', true);
                    $best_time = get_post_meta($post_id, '_petting_zoo_best_time', true);

                    if ($visitor_tips || $best_time) : ?>
                        <section class="zoo-tips-section">
                            <h2>💡 Planning Your Visit</h2>

                            <?php if ($visitor_tips) : ?>
                                <div class="tips-panel">
                                    <h3>👨‍👩‍👧‍👦 Tips for Families</h3>
                                    <p><?php echo esc_html($visitor_tips); ?></p>
                                </div>
                            <?php endif; ?>

                            <?php if ($best_time) : ?>
                                <div class="tips-panel">
                                    <h3>🕐 Best Time to Visit</h3>
                                    <p><?php echo esc_html($best_time); ?></p>
                                </div>
                            <?php endif; ?>
                        </section>
                    <?php endif; ?>

                    <!-- FAQ Section -->
                    <?php
                    $faq_data = get_post_meta($post_id, '_petting_zoo_faq', true);
                    if ($faq_data) :
                        $faqs = json_decode($faq_data, true); ?>
                        <section class="zoo-faq-section">
                            <h2>❓ Frequently Asked Questions</h2>
                            <p class="section-description">Get answers to common questions about visiting <?php the_title(); ?>. Plan your perfect family day out with confidence!</p>

                            <div class="faq-accordion">
                                <?php foreach ($faqs as $index => $faq) : ?>
                                    <div class="faq-item">
                                        <div class="faq-question" onclick="toggleFAQ(<?php echo $index; ?>)">
                                            <h4><?php echo esc_html($faq['question']); ?></h4>
                                            <span class="faq-toggle">+</span>
                                        </div>
                                        <div class="faq-answer" id="faq-answer-<?php echo $index; ?>">
                                            <p><?php echo esc_html($faq['answer']); ?></p>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </section>
                    <?php endif; ?>

                    <!-- Photo Gallery Section -->
                    <section class="zoo-gallery-section">
                        <h2>📸 Photo Gallery</h2>
                        <p class="section-description">Take a visual tour of <?php the_title(); ?>. Click on any image to view it in full size.</p>

                        <div class="photo-gallery-grid">
                            <?php
                            $gallery_count = 0;
                            $max_gallery_items = 8;

                            // First, try to use pics from JSON data
                            if (!empty($pics)) {
                                foreach ($pics as $pic) :
                                    if ($gallery_count >= $max_gallery_items) break;
                                    $gallery_count++;

                                    $pic_url = isset($pic['url']) ? $pic['url'] : '';
                                    $pic_key = isset($pic['key']) ? $pic['key'] : "pic{$gallery_count}";

                                    if ($pic_url) : ?>
                                        <div class="gallery-item" onclick="openLightbox('<?php echo esc_url($pic_url); ?>', '<?php echo esc_attr(get_the_title()); ?> - <?php echo esc_attr($pic_key); ?>')">
                                            <img src="<?php echo esc_url($pic_url); ?>"
                                                 alt="<?php echo esc_attr(get_the_title()); ?> - <?php echo esc_attr($pic_key); ?>"
                                                 loading="lazy">
                                            <div class="gallery-overlay">
                                                <span class="magnify-icon">🔍</span>
                                            </div>
                                        </div>
                                    <?php endif;
                                endforeach;
                            }

                            // Fill remaining slots with placeholder images
                            for ($i = $gallery_count + 1; $i <= $max_gallery_items; $i++) :
                                $placeholder_num = str_pad($i, 2, '0', STR_PAD_LEFT);
                                $placeholder_url = "/wp-content/uploads/2025/06/pettingzoophoto ({$i}).webp";
                                ?>
                                <div class="gallery-item" onclick="openLightbox('<?php echo esc_url($placeholder_url); ?>', '<?php echo esc_attr(get_the_title()); ?> - Photo <?php echo $i; ?>')">
                                    <img src="<?php echo esc_url($placeholder_url); ?>"
                                         alt="<?php echo esc_attr(get_the_title()); ?> - Photo <?php echo $i; ?>"
                                         loading="lazy">
                                    <div class="gallery-overlay">
                                        <span class="magnify-icon">🔍</span>
                                    </div>
                                </div>
                            <?php endfor; ?>
                        </div>
                    </section>



                </div> <!-- End container -->

            </article>

        <?php endwhile; ?>

    </main>
</div>

<!-- Lightbox Modal -->
<div id="lightbox-modal" class="lightbox-modal" onclick="closeLightbox()">
    <div class="lightbox-content">
        <span class="lightbox-close" onclick="closeLightbox()">&times;</span>
        <img id="lightbox-image" src="" alt="">
        <div id="lightbox-caption"></div>
    </div>
</div>

<script>
// Get Directions Function
function getDirections() {
    <?php if ($latitude && $longitude) : ?>
        const lat = <?php echo esc_js($latitude); ?>;
        const lng = <?php echo esc_js($longitude); ?>;
        const url = `https://maps.google.com/maps?daddr=${lat},${lng}`;
        window.open(url, '_blank');
    <?php else : ?>
        <?php if ($address) : ?>
            const address = "<?php echo esc_js($address); ?>";
            const url = `https://maps.google.com/maps?daddr=${encodeURIComponent(address)}`;
            window.open(url, '_blank');
        <?php endif; ?>
    <?php endif; ?>
}

// FAQ Toggle Function
function toggleFAQ(index) {
    const answer = document.getElementById(`faq-answer-${index}`);
    const toggle = answer.previousElementSibling.querySelector('.faq-toggle');

    if (answer.style.display === 'block') {
        answer.style.display = 'none';
        toggle.textContent = '+';
    } else {
        // Close all other FAQs
        document.querySelectorAll('.faq-answer').forEach(item => {
            item.style.display = 'none';
        });
        document.querySelectorAll('.faq-toggle').forEach(item => {
            item.textContent = '+';
        });

        // Open this FAQ
        answer.style.display = 'block';
        toggle.textContent = '-';
    }
}

// Lightbox Functions
function openLightbox(imageSrc, caption) {
    const modal = document.getElementById('lightbox-modal');
    const image = document.getElementById('lightbox-image');
    const captionElement = document.getElementById('lightbox-caption');

    image.src = imageSrc;
    captionElement.textContent = caption;
    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden';
}

function closeLightbox() {
    const modal = document.getElementById('lightbox-modal');
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Google Maps Integration
function initZooMap() {
    <?php if ($latitude && $longitude) : ?>
        const mapElement = document.getElementById('zoo-google-map');
        if (mapElement && typeof google !== 'undefined') {
            const lat = <?php echo esc_js($latitude); ?>;
            const lng = <?php echo esc_js($longitude); ?>;
            const title = "<?php echo esc_js(get_the_title()); ?>";

            const map = new google.maps.Map(mapElement, {
                center: { lat: lat, lng: lng },
                zoom: 15,
                mapTypeId: google.maps.MapTypeId.ROADMAP
            });

            const marker = new google.maps.Marker({
                position: { lat: lat, lng: lng },
                map: map,
                title: title
            });

            const infoWindow = new google.maps.InfoWindow({
                content: `<div style="padding: 10px;"><strong>${title}</strong><br><?php echo esc_js($address); ?></div>`
            });

            marker.addListener('click', function() {
                infoWindow.open(map, marker);
            });
        }
    <?php endif; ?>
}

// Initialize map when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Load Google Maps API if not already loaded
    if (typeof google === 'undefined') {
        const script = document.createElement('script');
        script.src = 'https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&callback=initZooMap';
        script.async = true;
        script.defer = true;
        document.head.appendChild(script);
    } else {
        initZooMap();
    }
});

// Close lightbox with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeLightbox();
    }
});
</script>

<?php get_footer(); ?>

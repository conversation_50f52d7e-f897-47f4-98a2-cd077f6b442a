/*
Theme Name: GeneratePress Child - Petting Zoo Directory
Description: Child theme of GeneratePress for the Petting Zoo Directory website
Author: Custom Development
Template: generatepress
Version: 2.0.0
Text Domain: generatepress-child
*/

/* Import parent theme styles */
@import url("../generatepress/style.css");

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700;800&family=Inter:wght@400;500;600&display=swap');

/* ========================================
   DESIGN SYSTEM - NATURAL & TRUSTWORTHY
   ======================================== */

/* Color Palette - Natural, Earthy, Family-Friendly */
:root {
    --forest-green: #2F6130;
    --forest-green-light: #4a7c4d;
    --forest-green-dark: #1e3f20;
    --warm-brown: #8B5E3C;
    --warm-brown-light: #a67350;
    --soft-beige: #FDF7F0;
    --soft-beige-dark: #f5ede0;
    --sky-blue: #7DC8F7;
    --sky-blue-light: #9dd4f9;
    --sky-blue-dark: #5bb8f4;
    --dark-charcoal: #333333;
    --medium-gray: #666666;
    --light-gray: #f8f9fa;
    --white: #ffffff;
    --success-green: #28a745;
    --warning-yellow: #ffc107;
    --danger-red: #dc3545;
}

/* Typography - Friendly & Accessible */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: var(--dark-charcoal);
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 1rem;
    color: var(--dark-charcoal);
}

h1 { font-size: 2.5rem; font-weight: 800; }
h2 { font-size: 2rem; font-weight: 700; }
h3 { font-size: 1.5rem; font-weight: 600; }
h4 { font-size: 1.25rem; font-weight: 600; }

/* Enhanced Hero Section - Full Width with Background Image */
.hero-section {
    position: relative;
    width: 100%;
    height: 80vh;
    min-height: 600px;
    max-height: 800px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
}

/* Ensure full width for hero section in GeneratePress */
.full-width-content .hero-section {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    margin-right: calc(-50vw + 50%);
}

/* Override GeneratePress container constraints for hero */
.hero-section .container {
    max-width: none;
    width: 100%;
}

/* Ensure GeneratePress doesn't interfere with full width */
.full-width-content {
    width: 100% !important;
    max-width: none !important;
}

.full-width-content .site-main {
    width: 100% !important;
    max-width: none !important;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    display: block;
}

/* Fallback background if image doesn't load */
.hero-section {
    background: linear-gradient(135deg, var(--forest-green) 0%, var(--warm-brown) 100%);
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(47, 97, 48, 0.8) 0%, rgba(30, 63, 32, 0.9) 100%);
    z-index: 2;
}

.hero-content {
    position: relative;
    z-index: 3;
    color: var(--white);
    text-align: center;
    width: 100%;
    padding: 0 20px;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.08)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.06)"/></svg>');
    pointer-events: none;
}

.hero-section h1 {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    font-weight: 800;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2;
}

.hero-section p {
    font-size: 1.3rem;
    margin-bottom: 3rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    opacity: 0.95;
    position: relative;
    z-index: 2;
}

/* Enhanced Zoo Finder Tool - Dad-Friendly UX */
.zoo-finder {
    background: var(--white);
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    margin-top: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    z-index: 2;
    border: 3px solid var(--soft-beige);
}

.zoo-finder h3 {
    color: var(--forest-green);
    margin-bottom: 1.5rem;
    font-size: 1.4rem;
    font-weight: 700;
    text-align: center;
}

.zoo-finder select,
.zoo-finder input {
    width: 100%;
    padding: 16px 20px;
    margin-bottom: 1.5rem;
    border: 2px solid var(--soft-beige-dark);
    border-radius: 10px;
    font-size: 1.1rem;
    font-family: 'Inter', sans-serif;
    background: var(--soft-beige);
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.zoo-finder select:focus,
.zoo-finder input:focus {
    outline: none;
    border-color: var(--sky-blue);
    box-shadow: 0 0 0 3px rgba(125, 200, 247, 0.2);
    background: var(--white);
}

.zoo-finder button {
    background: linear-gradient(135deg, var(--forest-green) 0%, var(--forest-green-light) 100%);
    color: var(--white);
    padding: 16px 30px;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    width: 100%;
    transition: all 0.3s ease;
    margin-bottom: 1rem;
    min-height: 48px; /* Mobile-friendly tap target */
    font-family: 'Nunito', sans-serif;
}

.zoo-finder button:hover {
    background: linear-gradient(135deg, var(--forest-green-light) 0%, var(--forest-green) 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(47, 97, 48, 0.3);
}

.zoo-finder .btn-secondary {
    background: linear-gradient(135deg, var(--warm-brown) 0%, var(--warm-brown-light) 100%);
    margin-bottom: 0;
}

.zoo-finder .btn-secondary:hover {
    background: linear-gradient(135deg, var(--warm-brown-light) 0%, var(--warm-brown) 100%);
    box-shadow: 0 4px 15px rgba(139, 94, 60, 0.3);
}

/* ========================================
   ENHANCED GRID LAYOUTS - MOBILE FIRST
   ======================================== */

.zoo-grid,
.city-grid,
.animal-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

/* Enhanced Zoo Card Design - Trust & Visual Hierarchy */
.zoo-card {
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 2px solid var(--soft-beige);
    position: relative;
}

.zoo-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 30px rgba(47, 97, 48, 0.15);
    border-color: var(--forest-green);
}

.zoo-card img {
    width: 100%;
    height: 220px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.zoo-card:hover img {
    transform: scale(1.05);
}

.zoo-card-content {
    padding: 2rem;
    position: relative;
}

.zoo-card h3 {
    margin-bottom: 0.75rem;
    color: var(--forest-green);
    font-size: 1.3rem;
    font-weight: 700;
}

.zoo-card .location {
    color: var(--medium-gray);
    margin-bottom: 1.25rem;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.zoo-card .location::before {
    content: "📍";
    font-size: 1.1rem;
}

.zoo-card .features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.zoo-card .feature-tag {
    background: linear-gradient(135deg, var(--soft-beige) 0%, var(--soft-beige-dark) 100%);
    color: var(--forest-green);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    border: 1px solid var(--forest-green);
    transition: all 0.3s ease;
}

.zoo-card .feature-tag:hover {
    background: var(--forest-green);
    color: var(--white);
    transform: translateY(-2px);
}

/* ========================================
   ENHANCED SECTION STYLES - VISUAL HIERARCHY
   ======================================== */

.section {
    padding: 5rem 0;
    position: relative;
}

.section-title {
    text-align: center;
    font-size: 2.75rem;
    margin-bottom: 1.5rem;
    color: var(--forest-green);
    font-weight: 800;
    position: relative;
}

.section-title::after {
    content: '';
    display: block;
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--sky-blue) 0%, var(--forest-green) 100%);
    margin: 1rem auto;
    border-radius: 2px;
}

.section-subtitle {
    text-align: center;
    font-size: 1.3rem;
    color: var(--medium-gray);
    margin-bottom: 4rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

/* Enhanced Popular Cities Section */
.popular-cities-section {
    padding: 80px 0;
    background: var(--soft-beige);
    width: 100%;
}

/* Ensure full width for all sections */
.full-width-content .section {
    width: 100%;
    margin: 0;
}

/* Ensure full width for popular cities section */
.full-width-content .popular-cities-section {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    margin-right: calc(-50vw + 50%);
}

/* No data message styling */
.no-data-message {
    text-align: center;
    padding: 3rem;
    background: var(--white);
    border-radius: 12px;
    border: 2px dashed var(--soft-beige-dark);
    color: var(--medium-gray);
    font-style: italic;
}

.state-cards-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin: 3rem 0;
}

.state-card {
    background: var(--white);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 2px solid var(--soft-beige-dark);
    position: relative;
    overflow: hidden;
}

.state-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--forest-green) 0%, var(--sky-blue) 100%);
}

.state-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(47, 97, 48, 0.15);
    border-color: var(--forest-green);
}

.state-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--forest-green);
    margin-bottom: 1.5rem;
    text-align: center;
    border-bottom: 2px solid var(--soft-beige-dark);
    padding-bottom: 1rem;
}

.cities-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.city-link {
    color: var(--medium-gray);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
    border-left: 3px solid transparent;
}

.city-link:hover {
    background: var(--soft-beige);
    color: var(--forest-green);
    border-left-color: var(--forest-green);
    text-decoration: none;
    transform: translateX(5px);
}

/* Legacy city cards for backward compatibility */
.cities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.city-card {
    background: var(--white);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 2px solid var(--soft-beige);
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.city-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--sky-blue) 0%, var(--forest-green) 100%);
}

.city-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(47, 97, 48, 0.15);
    border-color: var(--forest-green);
    text-decoration: none;
}

.city-card h4 {
    color: var(--forest-green);
    margin-bottom: 1rem;
    font-size: 1.3rem;
    font-weight: 700;
}

.city-card .zoo-count {
    color: var(--medium-gray);
    font-size: 1rem;
    font-weight: 500;
    background: var(--soft-beige);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    display: inline-block;
}

/* Enhanced Animal Types Grid - Playful & Engaging */
.animals-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.animal-card {
    background: var(--white);
    border-radius: 15px;
    padding: 2rem 1.5rem;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 2px solid var(--soft-beige);
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.animal-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--sky-blue) 0%, var(--warm-brown) 100%);
}

.animal-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 8px 25px rgba(125, 200, 247, 0.2);
    border-color: var(--sky-blue);
    text-decoration: none;
}

.animal-card .animal-icon {
    font-size: 3.5rem;
    margin-bottom: 1.25rem;
    transition: transform 0.3s ease;
}

.animal-card:hover .animal-icon {
    transform: scale(1.1) rotate(5deg);
}

.animal-card h4 {
    color: var(--forest-green);
    margin-bottom: 0.75rem;
    font-size: 1.2rem;
    font-weight: 700;
}

.animal-card .zoo-count {
    color: var(--medium-gray);
    font-size: 0.95rem;
    font-weight: 500;
    background: var(--soft-beige);
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    display: inline-block;
}

/* ========================================
   ENHANCED FAQ SECTION - EXPANDABLE & ACCESSIBLE
   ======================================== */

.faq-section {
    background: var(--soft-beige);
    position: relative;
}

.faq-item {
    background: var(--white);
    margin-bottom: 1.5rem;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border: 2px solid var(--soft-beige-dark);
    transition: all 0.3s ease;
}

.faq-item:hover {
    border-color: var(--forest-green);
    box-shadow: 0 6px 20px rgba(47, 97, 48, 0.1);
}

.faq-question {
    background: linear-gradient(135deg, var(--forest-green) 0%, var(--forest-green-light) 100%);
    color: var(--white);
    padding: 1.5rem 2rem;
    cursor: pointer;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    position: relative;
    min-height: 48px; /* Mobile-friendly tap target */
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.faq-question::after {
    content: '+';
    font-size: 1.5rem;
    font-weight: 700;
    transition: transform 0.3s ease;
}

.faq-question.active::after {
    transform: rotate(45deg);
}

.faq-question:hover {
    background: linear-gradient(135deg, var(--forest-green-light) 0%, var(--forest-green) 100%);
}

.faq-answer {
    padding: 0;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-answer.active {
    padding: 2rem;
    max-height: 500px;
}

.faq-answer p {
    margin: 0;
    color: var(--dark-charcoal);
    line-height: 1.7;
    font-size: 1.05rem;
}

/* ========================================
   ENHANCED BUTTON SYSTEM - TRUST & ACCESSIBILITY
   ======================================== */

.btn {
    display: inline-block;
    padding: 16px 32px;
    background: linear-gradient(135deg, var(--forest-green) 0%, var(--forest-green-light) 100%);
    color: var(--white);
    text-decoration: none;
    border-radius: 10px;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1.1rem;
    font-weight: 600;
    font-family: 'Nunito', sans-serif;
    min-height: 48px; /* Mobile-friendly tap target */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    box-shadow: 0 4px 15px rgba(47, 97, 48, 0.2);
}

.btn:hover {
    background: linear-gradient(135deg, var(--forest-green-light) 0%, var(--forest-green) 100%);
    color: var(--white);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(47, 97, 48, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, var(--warm-brown) 0%, var(--warm-brown-light) 100%);
    box-shadow: 0 4px 15px rgba(139, 94, 60, 0.2);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, var(--warm-brown-light) 0%, var(--warm-brown) 100%);
    box-shadow: 0 6px 20px rgba(139, 94, 60, 0.3);
}

.btn-sky {
    background: linear-gradient(135deg, var(--sky-blue) 0%, var(--sky-blue-dark) 100%);
    box-shadow: 0 4px 15px rgba(125, 200, 247, 0.2);
}

.btn-sky:hover {
    background: linear-gradient(135deg, var(--sky-blue-dark) 0%, var(--sky-blue) 100%);
    box-shadow: 0 6px 20px rgba(125, 200, 247, 0.3);
}

/* ========================================
   MOBILE-FIRST RESPONSIVE DESIGN
   ======================================== */

@media (max-width: 768px) {
    .hero-section {
        height: 70vh;
        min-height: 500px;
        max-height: 600px;
        padding: 0;
    }

    .hero-content {
        padding: 0 15px;
    }

    .hero-section h1 {
        font-size: 2.2rem;
        margin-bottom: 1rem;
    }

    .hero-section p {
        font-size: 1.1rem;
        margin-bottom: 2rem;
    }

    /* Ensure mobile full width */
    .full-width-content .hero-section {
        width: 100vw;
        margin-left: calc(-50vw + 50%);
        margin-right: calc(-50vw + 50%);
    }

    .full-width-content .popular-cities-section {
        width: 100vw;
        margin-left: calc(-50vw + 50%);
        margin-right: calc(-50vw + 50%);
    }

    .zoo-finder {
        padding: 2rem;
        margin-top: 2rem;
    }

    .zoo-grid,
    .city-grid,
    .animal-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .cities-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .animals-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }

    /* State cards responsive */
    .state-cards-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .state-card {
        padding: 1.5rem;
    }

    .state-title {
        font-size: 1.1rem;
        margin-bottom: 1rem;
    }

    .section-title {
        font-size: 2.2rem;
    }

    .section {
        padding: 3rem 0;
    }
}

/* Tablet responsive styles */
@media (max-width: 1024px) and (min-width: 769px) {
    .state-cards-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
}

/* ========================================
   UTILITY CLASSES - DESIGN SYSTEM
   ======================================== */

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1.5rem;
    width: 100%;
}

/* Full width container for hero section */
.hero-content .container {
    max-width: 1200px;
}

.text-center {
    text-align: center;
}

.mb-2 {
    margin-bottom: 2rem;
}

.mt-2 {
    margin-top: 2rem;
}

.mb-3 {
    margin-bottom: 3rem;
}

.mt-3 {
    margin-top: 3rem;
}

/* ========================================
   REDESIGNED SINGLE ZOO PAGE - FULL WIDTH LAYOUT
   ======================================== */

/* Full-width content for zoo pages */
.zoo-single-page {
    width: 100%;
    max-width: none;
}

/* Hero Section Styling */
.zoo-hero-section {
    position: relative;
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    margin-right: calc(-50vw + 50%);
    height: 400px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 4rem;
}

.zoo-hero-section .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(47, 97, 48, 0.8) 0%, rgba(30, 63, 32, 0.9) 100%);
    z-index: 1;
}

.zoo-hero-section .hero-content-wrapper {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.zoo-hero-section .hero-content {
    text-align: center;
    color: var(--white);
    max-width: 800px;
}

.zoo-hero-section .zoo-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    color: var(--white);
}

.zoo-hero-section .zoo-address {
    font-size: 1.3rem;
    margin-bottom: 2.5rem;
    opacity: 0.95;
    color: var(--white);
    justify-content: center;
}

.zoo-hero-section .hero-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
}

.hero-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
    text-decoration: none;
    border-radius: 50px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    font-weight: 600;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.hero-btn:hover {
    background: var(--white);
    color: var(--forest-green);
    border-color: var(--white);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Info + Map Section */
.zoo-info-map-section {
    margin-bottom: 4rem;
    padding: 3rem 0;
    background: var(--white);
    border-radius: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 2px solid var(--soft-beige);
}

.info-map-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.basic-info-panel h2,
.map-panel h3 {
    color: var(--forest-green);
    margin-bottom: 2rem;
    font-size: 2rem;
    font-weight: 700;
}

.zoo-description {
    margin-bottom: 2rem;
}

.zoo-description h3 {
    color: var(--forest-green);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.zoo-rating {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--soft-beige);
    border-radius: 15px;
}

.zoo-rating h4 {
    color: var(--forest-green);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.rating-display {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.rating-stars {
    font-size: 1.2rem;
}

.rating-text {
    font-weight: 600;
    color: var(--dark-charcoal);
}

.opening-hours {
    margin-bottom: 2rem;
}

.opening-hours h4 {
    color: var(--forest-green);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.hours-list {
    background: var(--soft-beige);
    border-radius: 10px;
    padding: 1rem;
}

.hours-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(47, 97, 48, 0.1);
}

.hours-row:last-child {
    border-bottom: none;
}

.hours-row .day {
    font-weight: 600;
    color: var(--forest-green);
    flex: 1;
}

.hours-row .time {
    color: var(--dark-charcoal);
    text-align: right;
}

.hours-row .full-line {
    color: var(--dark-charcoal);
    text-align: center;
    width: 100%;
}

.hours-list {
    background: var(--soft-beige);
    padding: 1.5rem;
    border-radius: 10px;
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
}

.contact-info h4 {
    color: var(--forest-green);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    padding: 0.75rem;
    background: rgba(47, 97, 48, 0.05);
    border-radius: 8px;
}

.contact-item .icon {
    font-size: 1.2rem;
}

.contact-item a {
    color: var(--forest-green);
    text-decoration: none;
    font-weight: 500;
}

.contact-item a:hover {
    text-decoration: underline;
}

/* Map Panel */
.zoo-map-container {
    background: var(--soft-beige);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

#zoo-google-map {
    width: 100%;
    height: 350px;
    border: none;
}

.map-actions {
    padding: 1.5rem;
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 0.9rem;
}

.btn-primary {
    background: var(--forest-green);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--warm-brown);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(47, 97, 48, 0.3);
}

.btn-secondary {
    background: var(--soft-beige);
    color: var(--forest-green);
    border: 2px solid var(--forest-green);
}

.btn-secondary:hover {
    background: var(--forest-green);
    color: var(--white);
}

.btn-outline {
    background: transparent;
    color: var(--forest-green);
    border: 2px solid var(--forest-green);
}

.btn-outline:hover {
    background: var(--forest-green);
    color: var(--white);
}

.quick-info-item {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, var(--forest-green) 0%, var(--forest-green-light) 100%);
    color: var(--white);
    text-decoration: none;
    border-radius: 10px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    min-height: 48px; /* Mobile-friendly tap target */
    box-shadow: 0 4px 15px rgba(47, 97, 48, 0.2);
}

.quick-info-item:hover {
    background: linear-gradient(135deg, var(--forest-green-light) 0%, var(--forest-green) 100%);
    color: var(--white);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(47, 97, 48, 0.3);
}

.zoo-content-wrapper {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    margin-top: 3rem;
}

.zoo-main-content section {
    margin-bottom: 3rem;
    padding: 2.5rem;
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 2px solid var(--soft-beige);
    transition: all 0.3s ease;
}

.zoo-main-content section:hover {
    border-color: var(--forest-green);
    box-shadow: 0 6px 25px rgba(47, 97, 48, 0.1);
}

.zoo-main-content section h3 {
    color: var(--forest-green);
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 3px solid var(--soft-beige);
}

.zoo-rating {
    background: linear-gradient(135deg, var(--soft-beige) 0%, var(--soft-beige-dark) 100%);
    padding: 1.5rem;
    border-radius: 12px;
    margin-top: 1.5rem;
    border: 2px solid var(--forest-green);
}

.rating-stars {
    font-size: 1.4rem;
    margin: 0 0.75rem;
    color: var(--warning-yellow);
}

.exotic-animals-notice {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 2px solid var(--warning-yellow);
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    color: #856404;
    font-weight: 600;
}

.animal-tags, .features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-top: 1.5rem;
}

.animal-tag, .feature-tag {
    background: linear-gradient(135deg, var(--soft-beige) 0%, var(--soft-beige-dark) 100%);
    color: var(--forest-green);
    padding: 0.75rem 1.25rem;
    border-radius: 25px;
    text-decoration: none;
    font-size: 0.95rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid var(--forest-green);
}

.animal-tag:hover, .feature-tag:hover {
    background: var(--forest-green);
    color: var(--white);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(47, 97, 48, 0.2);
}

.activities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.activity-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem;
    background: var(--light-gray);
    border-radius: 12px;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid var(--soft-beige-dark);
}

.activity-item.available {
    background: linear-gradient(135deg, var(--soft-beige) 0%, var(--soft-beige-dark) 100%);
    border-color: var(--forest-green);
}

.activity-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.activity-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    transition: transform 0.3s ease;
}

.activity-item:hover .activity-icon {
    transform: scale(1.1);
}

.activity-label {
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--forest-green);
    font-size: 1.1rem;
}

.availability {
    font-size: 0.9rem;
    color: var(--forest-green);
    font-weight: 600;
}

.pros-cons-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2.5rem;
    margin-top: 2rem;
}

.pros-section, .cons-section {
    padding: 2rem;
    border-radius: 15px;
    border: 2px solid;
    transition: all 0.3s ease;
}

.pros-section {
    background: linear-gradient(135deg, var(--soft-beige) 0%, var(--soft-beige-dark) 100%);
    border-color: var(--forest-green);
}

.cons-section {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-color: var(--warning-yellow);
}

.pros-section:hover, .cons-section:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.pros-section h3 {
    color: var(--forest-green);
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    font-weight: 700;
}

.cons-section h3 {
    color: #856404;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    font-weight: 700;
}

.pros-section ul, .cons-section ul {
    list-style: none;
    padding: 0;
}

.pros-section li, .cons-section li {
    padding: 0.5rem 0;
    position: relative;
    padding-left: 2rem;
    font-size: 1.05rem;
    line-height: 1.6;
}

.pros-section li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--forest-green);
    font-weight: bold;
    font-size: 1.2rem;
}

.cons-section li:before {
    content: "⚠";
    position: absolute;
    left: 0;
    color: #856404;
    font-size: 1.2rem;
}

/* Enhanced Sidebar - Information & Trust Signals */
.zoo-sidebar {
    display: flex;
    flex-direction: column;
    gap: 2.5rem;
}

.sidebar-widget {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 2px solid var(--soft-beige);
    transition: all 0.3s ease;
}

.sidebar-widget:hover {
    border-color: var(--forest-green);
    box-shadow: 0 6px 25px rgba(47, 97, 48, 0.1);
}

.sidebar-widget h3 {
    margin-bottom: 1.5rem;
    color: var(--forest-green);
    border-bottom: 3px solid var(--sky-blue);
    padding-bottom: 0.75rem;
    font-size: 1.3rem;
    font-weight: 700;
}

.info-section {
    margin-bottom: 2rem;
}

.info-section h4 {
    color: var(--forest-green);
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
}

.location-link, .zoo-type-link {
    display: block;
    padding: 1rem;
    background: var(--soft-beige);
    border-radius: 10px;
    text-decoration: none;
    color: var(--dark-charcoal);
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
    border: 2px solid var(--soft-beige-dark);
    font-weight: 500;
}

.location-link:hover, .zoo-type-link:hover {
    background: var(--forest-green);
    color: var(--white);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(47, 97, 48, 0.2);
}

.map-placeholder {
    text-align: center;
    padding: 3rem 2rem;
    background: linear-gradient(135deg, var(--soft-beige) 0%, var(--soft-beige-dark) 100%);
    border-radius: 15px;
    border: 2px solid var(--forest-green);
    color: var(--medium-gray);
    font-size: 1.1rem;
}

/* ========================================
   NEW SINGLE ZOO PAGE SECTIONS STYLING
   ======================================== */

/* Section Styling */
.zoo-single-page section {
    margin-bottom: 4rem;
    padding: 3rem;
    background: var(--white);
    border-radius: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 2px solid var(--soft-beige);
    transition: all 0.3s ease;
}

.zoo-single-page section:hover {
    border-color: var(--forest-green);
    box-shadow: 0 6px 25px rgba(47, 97, 48, 0.1);
}

.zoo-single-page section h2 {
    color: var(--forest-green);
    font-size: 2.2rem;
    margin-bottom: 1.5rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.section-description {
    font-size: 1.1rem;
    color: var(--medium-gray);
    margin-bottom: 2.5rem;
    line-height: 1.6;
}

/* Video Section */
.zoo-video-section .video-container {
    max-width: 800px;
    margin: 0 auto;
}

.responsive-video {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    height: 0;
    overflow: hidden;
    border-radius: 15px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.responsive-video iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 15px;
}

.video-link {
    text-align: center;
    padding: 3rem;
}

.video-link .btn {
    font-size: 1.1rem;
    padding: 1rem 2rem;
}

/* Animals Section */
.zoo-animals-section .animal-tags-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.animal-tag {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: 1.5rem 1rem;
    background: linear-gradient(135deg, var(--soft-beige) 0%, rgba(253, 247, 240, 0.8) 100%);
    border: 2px solid var(--soft-beige);
    border-radius: 15px;
    text-decoration: none;
    color: var(--forest-green);
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    text-align: center;
    min-height: 120px;
    justify-content: center;
}

.animal-tag:hover {
    background: var(--forest-green);
    color: var(--white);
    border-color: var(--forest-green);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(47, 97, 48, 0.2);
}

.animal-icon-large {
    font-size: 3rem;
    line-height: 1;
}

.animal-name {
    font-size: 0.9rem;
    line-height: 1.2;
}

.exotic-animals-notice {
    background: linear-gradient(135deg, var(--sky-blue) 0%, rgba(135, 206, 235, 0.8) 100%);
    color: var(--white);
    padding: 1.5rem;
    border-radius: 15px;
    margin: 2rem 0;
    display: flex;
    align-items: center;
    gap: 1rem;
    font-weight: 600;
    font-size: 1.1rem;
    box-shadow: 0 4px 15px rgba(135, 206, 235, 0.3);
}

.exotic-animals-notice .icon {
    font-size: 1.8rem;
}

/* Detailed Information Section */
.info-categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.info-category {
    background: var(--soft-beige);
    padding: 2rem;
    border-radius: 15px;
    border: 2px solid rgba(47, 97, 48, 0.1);
    transition: all 0.3s ease;
}

.info-category:hover {
    border-color: var(--forest-green);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(47, 97, 48, 0.1);
}

.info-category h3 {
    color: var(--forest-green);
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
    font-weight: 700;
}

.info-items {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: var(--white);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.info-item:hover {
    background: rgba(47, 97, 48, 0.05);
}

.info-icon {
    font-size: 1.2rem;
    min-width: 24px;
}

.info-label {
    font-weight: 500;
    color: var(--dark-charcoal);
}

/* Features & Amenities Section */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    background: var(--soft-beige);
    border-radius: 12px;
    border: 2px solid rgba(47, 97, 48, 0.1);
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: var(--forest-green);
    color: var(--white);
    border-color: var(--forest-green);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(47, 97, 48, 0.2);
}

.feature-icon {
    font-size: 1.3rem;
}

.feature-name {
    font-weight: 600;
}

.food-amenities {
    background: var(--soft-beige);
    padding: 2rem;
    border-radius: 15px;
    margin-top: 2rem;
}

.food-amenities h3 {
    color: var(--forest-green);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

/* Events Section */
.events-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.event-card {
    background: var(--soft-beige);
    padding: 2rem;
    border-radius: 15px;
    border: 2px solid rgba(47, 97, 48, 0.1);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.event-card:hover {
    border-color: var(--forest-green);
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(47, 97, 48, 0.15);
}

.event-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.event-content h4 {
    color: var(--forest-green);
    margin-bottom: 1rem;
    font-size: 1.2rem;
    font-weight: 700;
}

.event-content p {
    color: var(--medium-gray);
    margin-bottom: 1.5rem;
    line-height: 1.5;
}

.availability-badge {
    background: var(--forest-green);
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

/* ========================================
   ENHANCED MOBILE RESPONSIVENESS
   ======================================== */

@media (max-width: 768px) {
    .zoo-hero-section {
        height: 350px;
    }

    .zoo-hero-section .hero-content-wrapper {
        padding: 0 1rem;
    }

    .zoo-hero-section .zoo-title {
        font-size: 2.5rem;
    }

    .zoo-hero-section .zoo-address {
        font-size: 1.1rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .hero-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .info-map-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .zoo-single-page section {
        padding: 2rem;
        margin-bottom: 2.5rem;
    }

    .zoo-single-page section h2 {
        font-size: 1.8rem;
    }

    .info-categories-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .events-grid {
        grid-template-columns: 1fr;
    }

    .animal-tags-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 0.75rem;
    }

    .animal-tag {
        padding: 1rem 0.5rem;
        min-height: 100px;
    }

    .animal-icon-large {
        font-size: 2.5rem;
    }

    .animal-name {
        font-size: 0.8rem;
    }
}

/* ========================================
   TESTIMONIALS & REVIEWS SECTION
   ======================================== */

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.testimonial-card {
    background: var(--soft-beige);
    padding: 2rem;
    border-radius: 15px;
    border: 2px solid rgba(47, 97, 48, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.testimonial-card:hover {
    border-color: var(--forest-green);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(47, 97, 48, 0.1);
}

.testimonial-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.reviewer-info h4 {
    color: var(--forest-green);
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
    font-weight: 700;
}

.review-date {
    color: var(--medium-gray);
    font-size: 0.9rem;
}

.review-rating {
    display: flex;
    gap: 0.2rem;
}

.star.filled {
    color: #ffd700;
}

.star.empty {
    color: #ddd;
}

.testimonial-content p {
    font-style: italic;
    line-height: 1.6;
    color: var(--dark-charcoal);
}

.reviews-summary {
    background: var(--soft-beige);
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 3rem;
}

.reviews-summary h3 {
    color: var(--forest-green);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

/* ========================================
   GOOGLE REVIEWS HIGHLIGHT SECTION
   ======================================== */

.google-reviews-highlight {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 3px solid var(--forest-green);
    border-radius: 20px;
    padding: 2rem;
    margin: 2rem 0;
    text-align: center;
    box-shadow: 0 8px 25px rgba(47, 97, 48, 0.15);
}

.google-reviews-header h3 {
    color: var(--forest-green);
    font-size: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.rating-showcase {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.rating-number {
    font-size: 4rem;
    font-weight: 800;
    color: var(--forest-green);
    line-height: 1;
}

.rating-stars-large {
    display: flex;
    gap: 0.25rem;
    margin: 0.5rem 0;
}

.rating-stars-large .star {
    font-size: 2rem;
}

.rating-count {
    font-size: 1.2rem;
    color: var(--dark-charcoal);
    font-weight: 600;
}

.reviews-summary {
    margin: 2rem 0;
}

.reviews-summary .summary-text {
    font-size: 1.1rem;
    line-height: 1.6;
    color: var(--dark-charcoal);
    font-style: italic;
    background: var(--soft-beige);
    padding: 1.5rem;
    border-radius: 15px;
    border-left: 4px solid var(--forest-green);
}

/* ========================================
   EXPECTATIONS (PROS/CONS) SECTION
   ======================================== */

.expectations-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.pros-panel, .cons-panel {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.pros-panel {
    border-color: #28a745;
}

.cons-panel {
    border-color: #ffc107;
}

.panel-header {
    padding: 1.5rem;
    text-align: center;
}

.pros-panel .panel-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.cons-panel .panel-header {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: white;
}

.panel-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.panel-content {
    padding: 1.5rem;
}

.expectation-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--soft-beige);
}

.expectation-item:last-child {
    border-bottom: none;
}

.item-icon {
    font-size: 1.2rem;
    flex-shrink: 0;
    margin-top: 0.1rem;
}

.item-text {
    flex: 1;
    line-height: 1.5;
    color: var(--dark-charcoal);
}

.pros-panel,
.cons-panel {
    background: var(--soft-beige);
    border-radius: 15px;
    overflow: hidden;
    border: 2px solid rgba(47, 97, 48, 0.1);
    transition: all 0.3s ease;
}

.pros-panel:hover,
.cons-panel:hover {
    border-color: var(--forest-green);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(47, 97, 48, 0.1);
}

.panel-header {
    background: var(--forest-green);
    color: var(--white);
    padding: 1.5rem;
}

.panel-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.panel-content {
    padding: 2rem;
}

.expectation-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--white);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.expectation-item:hover {
    background: rgba(47, 97, 48, 0.05);
    transform: translateX(5px);
}

.expectation-item:last-child {
    margin-bottom: 0;
}

.item-icon {
    font-size: 1.2rem;
    min-width: 24px;
}

.item-text {
    font-weight: 500;
    line-height: 1.5;
    color: var(--dark-charcoal);
}

.positive .item-icon {
    color: #28a745;
}

.consideration .item-icon {
    color: #ffc107;
}

/* ========================================
   NEARBY ZOOS SECTION
   ======================================== */

.nearby-zoos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.nearby-zoo-card {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 2px solid var(--soft-beige);
    transition: all 0.3s ease;
}

.nearby-zoo-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(47, 97, 48, 0.15);
    border-color: var(--forest-green);
}

.nearby-zoo-card .zoo-image {
    height: 200px;
    overflow: hidden;
}

.nearby-zoo-card .zoo-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.nearby-zoo-card:hover .zoo-image img {
    transform: scale(1.05);
}

.nearby-zoo-card .zoo-info {
    padding: 2rem;
}

.nearby-zoo-card h4 {
    margin-bottom: 1rem;
    font-size: 1.2rem;
    font-weight: 700;
}

.nearby-zoo-card h4 a {
    color: var(--forest-green);
    text-decoration: none;
}

.nearby-zoo-card h4 a:hover {
    text-decoration: underline;
}

.zoo-location {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    color: var(--medium-gray);
    font-size: 0.9rem;
}

.zoo-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.zoo-rating .stars {
    font-size: 0.9rem;
}

.rating-value {
    font-weight: 600;
    color: var(--dark-charcoal);
    font-size: 0.9rem;
}

.zoo-excerpt {
    color: var(--medium-gray);
    line-height: 1.5;
    margin-bottom: 1.5rem;
}

/* ========================================
   STATE CITIES SECTION
   ======================================== */

.cities-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.city-badge {
    display: inline-block;
    padding: 0.5rem 1rem;
    background: var(--soft-beige);
    color: var(--forest-green);
    text-decoration: none;
    border-radius: 20px;
    border: 2px solid rgba(47, 97, 48, 0.1);
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.city-badge:hover {
    background: var(--forest-green);
    color: var(--white);
    border-color: var(--forest-green);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(47, 97, 48, 0.2);
}

/* ========================================
   FAQ SECTION
   ======================================== */

.faq-accordion {
    max-width: 900px;
    margin: 0 auto;
}

.faq-item {
    background: var(--white);
    border-radius: 20px;
    margin-bottom: 1.5rem;
    border: 3px solid var(--soft-beige);
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.faq-item:hover {
    border-color: var(--forest-green);
    box-shadow: 0 8px 25px rgba(47, 97, 48, 0.15);
    transform: translateY(-2px);
}

.faq-question {
    padding: 2rem 2.5rem;
    background: linear-gradient(135deg, var(--forest-green) 0%, rgba(47, 97, 48, 0.9) 100%);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.faq-question:hover {
    background: linear-gradient(135deg, rgba(47, 97, 48, 0.9) 0%, var(--forest-green) 100%);
}

.faq-question h4 {
    margin: 0;
    color: var(--white);
    font-size: 1.2rem;
    font-weight: 700;
    flex: 1;
    line-height: 1.4;
}

.faq-toggle {
    font-size: 2rem;
    font-weight: bold;
    color: var(--white);
    transition: transform 0.3s ease;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.faq-answer {
    display: none;
    padding: 2.5rem;
    background: var(--white);
    border-top: 1px solid var(--soft-beige);
}

.faq-answer p {
    margin: 0;
    line-height: 1.7;
    color: var(--dark-charcoal);
    font-size: 1.05rem;
}

/* ========================================
   PHOTO GALLERY SECTION
   ======================================== */

.photo-gallery-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin-top: 2rem;
}

.gallery-item {
    position: relative;
    aspect-ratio: 1;
    border-radius: 15px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(47, 97, 48, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.magnify-icon {
    font-size: 2rem;
    color: var(--white);
}

/* ========================================
   LIGHTBOX MODAL
   ======================================== */

.lightbox-modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.lightbox-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    text-align: center;
}

.lightbox-close {
    position: absolute;
    top: -50px;
    right: 0;
    color: var(--white);
    font-size: 3rem;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.lightbox-close:hover {
    color: #ccc;
}

#lightbox-image {
    max-width: 100%;
    max-height: 80vh;
    border-radius: 10px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.5);
}

#lightbox-caption {
    color: var(--white);
    font-size: 1.1rem;
    margin-top: 1rem;
    font-weight: 500;
}

/* ========================================
   TIPS SECTION
   ======================================== */

.tips-panel {
    background: var(--soft-beige);
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    border: 2px solid rgba(47, 97, 48, 0.1);
    transition: all 0.3s ease;
}

.tips-panel:hover {
    border-color: var(--forest-green);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(47, 97, 48, 0.1);
}

.tips-panel:last-child {
    margin-bottom: 0;
}

.tips-panel h3 {
    color: var(--forest-green);
    margin-bottom: 1rem;
    font-size: 1.3rem;
    font-weight: 700;
}

.tips-panel p {
    line-height: 1.6;
    color: var(--dark-charcoal);
    margin: 0;
}

/* ========================================
   ADDITIONAL MOBILE RESPONSIVENESS
   ======================================== */

@media (max-width: 768px) {
    .expectations-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .google-reviews-highlight {
        padding: 1.5rem;
    }

    .rating-number {
        font-size: 3rem;
    }

    .rating-stars-large .star {
        font-size: 1.5rem;
    }

    .panel-header h3 {
        font-size: 1.1rem;
    }

    .panel-content {
        padding: 1rem;
    }

    .nearby-zoos-grid {
        grid-template-columns: 1fr;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .photo-gallery-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .faq-question {
        padding: 1.5rem 1.5rem;
    }

    .faq-question h4 {
        font-size: 1rem;
    }

    .faq-toggle {
        width: 35px;
        height: 35px;
        font-size: 1.5rem;
    }

    .faq-answer {
        padding: 1.5rem;
    }

    .tips-panel {
        padding: 1.5rem;
    }

    .lightbox-modal {
        padding: 1rem;
    }

    .lightbox-close {
        top: -40px;
        font-size: 2.5rem;
    }
}

/* ========================================
   ADDITIONAL ENHANCEMENTS - TRUST & ENGAGEMENT
   ======================================== */

/* Sticky Navigation Enhancement */
.site-header {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    background: var(--white);
    border-bottom: 3px solid var(--forest-green);
}

/* Loading States */
.loading {
    opacity: 0.7;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--forest-green);
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Focus States for Accessibility */
button:focus,
input:focus,
select:focus,
a:focus {
    outline: 3px solid var(--sky-blue);
    outline-offset: 2px;
}

/* Enhanced Touch Interactions for Mobile */
.touch-active {
    transform: scale(0.98) !important;
    transition: transform 0.1s ease !important;
}

.hover-active {
    transform: translateY(-3px);
}

/* Enhanced Form States */
.form-group.focused,
.zoo-finder.focused {
    transform: scale(1.02);
    transition: transform 0.2s ease;
}

input.error,
select.error {
    border-color: var(--danger-red) !important;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2) !important;
}

/* Enhanced Loading States */
.btn.loading {
    position: relative;
    color: transparent !important;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid var(--white);
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 0.8s linear infinite;
}

.form-group.loading {
    opacity: 0.7;
    pointer-events: none;
}

/* Enhanced Card Filtering Animation */
.zoo-card.filtering,
.city-card.filtering,
.animal-card.filtering {
    opacity: 0.5;
    transform: scale(0.95);
    transition: all 0.3s ease;
}

/* Improved Accessibility Focus States */
.zoo-card:focus,
.city-card:focus,
.animal-card:focus,
.btn:focus {
    outline: 3px solid var(--sky-blue);
    outline-offset: 3px;
    box-shadow: 0 0 0 6px rgba(125, 200, 247, 0.2);
}

/* Enhanced Mobile Navigation */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
        background: var(--forest-green);
        color: var(--white);
        border: none;
        padding: 1rem;
        border-radius: 8px;
        cursor: pointer;
        min-height: 48px;
    }

    .mobile-menu {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--white);
        z-index: 9999;
        padding: 2rem;
    }

    .mobile-menu.active {
        display: block;
    }
}

/* Print Styles */
@media print {
    .hero-section,
    .zoo-finder,
    .sidebar-widget {
        background: white !important;
        color: black !important;
    }

    .btn,
    .mobile-menu-toggle {
        display: none !important;
    }
}

/* ========================================
   ENHANCED CITY PAGE STYLES - SEO FOCUSED
   ======================================== */

/* City Page Header */
.city-page-header {
    background: linear-gradient(135deg, var(--forest-green) 0%, var(--forest-green-dark) 100%);
    color: var(--white);
    padding: 4rem 0;
    position: relative;
}

.city-page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.08)"/></svg>');
    pointer-events: none;
}

.city-intro-content {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 12px;
    margin-top: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.city-intro-content p {
    margin: 0;
    font-size: 1.1rem;
    line-height: 1.7;
    opacity: 0.95;
}

/* Enhanced Zoo Grid for City Pages */
.enhanced-zoo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2.5rem;
    margin: 3rem 0;
}

.enhanced-zoo-card {
    background: var(--white);
    border-radius: 16px;
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 2px solid var(--soft-beige);
    position: relative;
}

.enhanced-zoo-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(47, 97, 48, 0.2);
    border-color: var(--forest-green);
}

.enhanced-zoo-card .zoo-image-link {
    display: block;
    position: relative;
    overflow: hidden;
}

.enhanced-zoo-card img {
    width: 100%;
    height: 240px;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.enhanced-zoo-card:hover img {
    transform: scale(1.08);
}

.enhanced-zoo-card .zoo-card-content {
    padding: 2.5rem;
}

.enhanced-zoo-card h3 {
    margin-bottom: 1rem;
    color: var(--forest-green);
    font-size: 1.4rem;
    font-weight: 700;
}

.enhanced-zoo-card .location {
    color: var(--medium-gray);
    margin-bottom: 1.5rem;
    font-size: 1rem;
    font-weight: 500;
}

.enhanced-zoo-card .animal-types {
    background: var(--soft-beige);
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
    border-left: 4px solid var(--sky-blue);
}

.enhanced-zoo-card .animal-types strong {
    color: var(--forest-green);
    font-weight: 600;
}

.enhanced-zoo-card .more-animals {
    color: var(--sky-blue);
    font-weight: 600;
}

.enhanced-zoo-card .feature-tag {
    background: linear-gradient(135deg, var(--sky-blue) 0%, var(--sky-blue-dark) 100%);
    color: var(--white);
    padding: 0.6rem 1.2rem;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 600;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    display: inline-block;
    transition: all 0.3s ease;
}

.enhanced-zoo-card .feature-tag:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(125, 200, 247, 0.4);
}

.enhanced-zoo-card .more-features {
    background: linear-gradient(135deg, var(--warm-brown) 0%, var(--warm-brown-light) 100%);
}

.enhanced-zoo-card .btn-primary {
    background: linear-gradient(135deg, var(--forest-green) 0%, var(--forest-green-light) 100%);
    color: var(--white);
    padding: 1rem 2rem;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    width: 100%;
    text-align: center;
    margin-top: 1rem;
}

.enhanced-zoo-card .btn-primary:hover {
    background: linear-gradient(135deg, var(--forest-green-light) 0%, var(--forest-green) 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(47, 97, 48, 0.3);
    text-decoration: none;
    color: var(--white);
}

/* City Content Sections */
.city-zoos-section,
.city-family-benefits,
.city-features-section,
.city-tips-section,
.dad-focused-section,
.city-faq-section,
.finder-cta-section {
    margin: 4rem 0;
    padding: 3rem 0;
    position: relative;
}

.city-family-benefits,
.dad-focused-section {
    background: var(--soft-beige);
    border-radius: 20px;
    padding: 4rem 3rem;
    margin: 4rem 0;
}

.city-family-benefits h2,
.city-features-section h2,
.city-tips-section h2,
.dad-focused-section h2,
.city-faq-section h2,
.finder-cta-section h2 {
    color: var(--forest-green);
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-align: left;
}

.city-family-benefits p,
.city-features-section p,
.city-tips-section p,
.dad-focused-section p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--dark-charcoal);
    margin-bottom: 2.5rem;
}

/* Family Benefits Grid */
.family-benefits-grid,
.dad-benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2.5rem;
}

.benefit-item,
.dad-benefit {
    background: var(--white);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border-left: 4px solid var(--sky-blue);
    transition: all 0.3s ease;
}

.benefit-item:hover,
.dad-benefit:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(125, 200, 247, 0.15);
    border-left-color: var(--forest-green);
}

.benefit-item h4,
.dad-benefit h4 {
    color: var(--forest-green);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.benefit-item p,
.dad-benefit p {
    margin: 0;
    font-size: 1rem;
    line-height: 1.6;
    color: var(--medium-gray);
}

/* Features Grid */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.feature-item {
    background: var(--white);
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.08);
    border: 2px solid var(--soft-beige);
    transition: all 0.3s ease;
    text-align: center;
}

.feature-item:hover {
    transform: translateY(-3px);
    border-color: var(--sky-blue);
    box-shadow: 0 6px 20px rgba(125, 200, 247, 0.15);
}

.feature-item h4 {
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.feature-item h4 a {
    color: var(--forest-green);
    text-decoration: none;
    font-weight: 600;
}

.feature-item h4 a:hover {
    color: var(--sky-blue);
}

.feature-item p {
    margin: 0;
    font-size: 0.9rem;
    color: var(--medium-gray);
}

.features-cta {
    background: var(--sky-blue);
    color: var(--white);
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    margin-top: 2rem;
}

.features-cta p {
    margin: 0;
    font-size: 1.05rem;
    font-weight: 500;
}

/* Tips Section */
.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2.5rem;
}

.tip-category {
    background: var(--white);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border-top: 4px solid var(--warm-brown);
    transition: all 0.3s ease;
}

.tip-category:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(139, 94, 60, 0.15);
    border-top-color: var(--forest-green);
}

.tip-category h4 {
    color: var(--forest-green);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.tip-category ul {
    margin: 0;
    padding-left: 1.5rem;
    list-style-type: none;
}

.tip-category li {
    margin-bottom: 0.75rem;
    font-size: 1rem;
    line-height: 1.6;
    color: var(--medium-gray);
    position: relative;
}

.tip-category li::before {
    content: '✓';
    color: var(--success-green);
    font-weight: bold;
    position: absolute;
    left: -1.5rem;
}

/* Dad Testimonial */
.dad-testimonial {
    background: var(--white);
    padding: 2.5rem;
    border-radius: 15px;
    margin-top: 3rem;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    border-left: 5px solid var(--sky-blue);
    position: relative;
}

.dad-testimonial::before {
    content: '"';
    font-size: 4rem;
    color: var(--sky-blue);
    position: absolute;
    top: 1rem;
    left: 2rem;
    font-family: Georgia, serif;
    opacity: 0.3;
}

.dad-testimonial blockquote {
    margin: 0;
    padding-left: 3rem;
}

.dad-testimonial p {
    font-size: 1.2rem;
    line-height: 1.7;
    color: var(--dark-charcoal);
    font-style: italic;
    margin-bottom: 1rem;
}

.dad-testimonial cite {
    color: var(--forest-green);
    font-weight: 600;
    font-style: normal;
    font-size: 1rem;
}

/* FAQ Section */
.city-faq-section {
    background: var(--soft-beige);
    padding: 4rem 3rem;
    border-radius: 20px;
    margin: 4rem 0;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2.5rem;
}

.faq-item {
    background: var(--white);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    border: 2px solid var(--soft-beige-dark);
    transition: all 0.3s ease;
}

.faq-item:hover {
    border-color: var(--forest-green);
    box-shadow: 0 6px 20px rgba(47, 97, 48, 0.1);
    transform: translateY(-3px);
}

.faq-item h4 {
    background: linear-gradient(135deg, var(--forest-green) 0%, var(--forest-green-light) 100%);
    color: var(--white);
    padding: 1.5rem;
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    line-height: 1.4;
}

.faq-item p {
    padding: 1.5rem;
    margin: 0;
    font-size: 1rem;
    line-height: 1.6;
    color: var(--medium-gray);
}

/* CTA Section */
.finder-cta-section {
    background: linear-gradient(135deg, var(--forest-green) 0%, var(--forest-green-dark) 100%);
    color: var(--white);
    padding: 4rem 3rem;
    border-radius: 20px;
    margin: 4rem 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.finder-cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.08)"/></svg>');
    pointer-events: none;
}

.finder-cta-section h2 {
    color: var(--white);
    text-align: center;
    margin-bottom: 2rem;
    position: relative;
    z-index: 2;
}

.finder-cta-section p {
    color: var(--white);
    opacity: 0.95;
    font-size: 1.1rem;
    margin-bottom: 3rem;
    position: relative;
    z-index: 2;
}

.finder-cta-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.cta-benefits h4 {
    color: var(--white);
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
    text-align: left;
}

.cta-benefits ul {
    list-style: none;
    padding: 0;
    margin: 0;
    text-align: left;
}

.cta-benefits li {
    margin-bottom: 1rem;
    font-size: 1.05rem;
    position: relative;
    padding-left: 2rem;
}

.cta-benefits li::before {
    content: '🎯';
    position: absolute;
    left: 0;
    top: 0;
}

.cta-action {
    text-align: center;
}

.btn-large {
    padding: 1.5rem 3rem;
    font-size: 1.2rem;
    font-weight: 700;
    border-radius: 15px;
    background: linear-gradient(135deg, var(--sky-blue) 0%, var(--sky-blue-dark) 100%);
    color: var(--white);
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(125, 200, 247, 0.3);
}

.btn-large:hover {
    background: linear-gradient(135deg, var(--sky-blue-dark) 0%, var(--sky-blue) 100%);
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(125, 200, 247, 0.4);
    text-decoration: none;
    color: var(--white);
}

.cta-subtext {
    margin-top: 1rem;
    font-size: 0.95rem;
    opacity: 0.8;
    color: var(--white);
}

/* ========================================
   ENHANCED RESPONSIVE DESIGN FOR CITY PAGES
   ======================================== */

/* Tablet Devices (768px to 1024px) */
@media (max-width: 1024px) {
    .enhanced-zoo-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
    }

    .finder-cta-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .cta-benefits {
        text-align: center;
    }

    .cta-benefits h4,
    .cta-benefits ul {
        text-align: center;
    }
}

/* Mobile Devices (up to 768px) */
@media (max-width: 768px) {
    .city-page-header {
        padding: 3rem 0;
    }

    .city-intro-content {
        padding: 1.5rem;
        margin-top: 1.5rem;
    }

    .city-intro-content p {
        font-size: 1rem;
    }

    .enhanced-zoo-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .enhanced-zoo-card .zoo-card-content {
        padding: 2rem;
    }

    .enhanced-zoo-card img {
        height: 200px;
    }

    .city-family-benefits,
    .dad-focused-section,
    .city-faq-section,
    .finder-cta-section {
        padding: 2.5rem 1.5rem;
        margin: 2rem 0;
    }

    .city-family-benefits h2,
    .city-features-section h2,
    .city-tips-section h2,
    .dad-focused-section h2,
    .city-faq-section h2,
    .finder-cta-section h2 {
        font-size: 1.8rem;
    }

    .family-benefits-grid,
    .dad-benefits-grid,
    .features-grid,
    .tips-grid,
    .faq-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .benefit-item,
    .dad-benefit,
    .tip-category {
        padding: 1.5rem;
    }

    .dad-testimonial {
        padding: 2rem;
    }

    .dad-testimonial::before {
        font-size: 3rem;
        top: 0.5rem;
        left: 1.5rem;
    }

    .dad-testimonial blockquote {
        padding-left: 2.5rem;
    }

    .dad-testimonial p {
        font-size: 1.1rem;
    }

    .finder-cta-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .btn-large {
        padding: 1.25rem 2.5rem;
        font-size: 1.1rem;
        width: 100%;
        max-width: 300px;
    }

    .cta-benefits li {
        font-size: 1rem;
    }
}

/* Small Mobile Devices (up to 480px) */
@media (max-width: 480px) {
    .city-page-header {
        padding: 2rem 0;
    }

    .city-intro-content {
        padding: 1rem;
        margin-top: 1rem;
    }

    .enhanced-zoo-card .zoo-card-content {
        padding: 1.5rem;
    }

    .city-family-benefits,
    .dad-focused-section,
    .city-faq-section,
    .finder-cta-section {
        padding: 2rem 1rem;
        margin: 1.5rem 0;
    }

    .city-family-benefits h2,
    .city-features-section h2,
    .city-tips-section h2,
    .dad-focused-section h2,
    .city-faq-section h2,
    .finder-cta-section h2 {
        font-size: 1.6rem;
    }

    .benefit-item,
    .dad-benefit,
    .tip-category {
        padding: 1.25rem;
    }

    .dad-testimonial {
        padding: 1.5rem;
    }

    .dad-testimonial blockquote {
        padding-left: 2rem;
    }

    .btn-large {
        padding: 1rem 2rem;
        font-size: 1rem;
    }

    .enhanced-zoo-card .feature-tag {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
        margin-right: 0.25rem;
        margin-bottom: 0.25rem;
    }
}
